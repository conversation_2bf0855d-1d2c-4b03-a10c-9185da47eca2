import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../services/auth_service.dart';
import '../services/home_cache_service.dart';
import '../themes/app_theme.dart';
import 'item_detail_screen.dart';

class HomePageScreen extends StatefulWidget {
  final bool showWelcomeMessage;

  const HomePageScreen({super.key, this.showWelcomeMessage = false});

  @override
  State<HomePageScreen> createState() => _HomePageScreenState();
}

class _HomePageScreenState extends State<HomePageScreen> {
  bool _showWelcome = false;
  bool _isFirstTimeUser = false;
  double? _currentLatitude;
  double? _currentLongitude;
  String? _currentCity;
  bool _isRefreshing = false;
  bool _hasInitialData = false;
  List<Map<String, dynamic>> _sortedItems = [];

  @override
  void initState() {
    super.initState();
    if (widget.showWelcomeMessage) {
      _initializeWelcomeMessage();
    }
    _initializeHomeScreen();
  }

  Future<void> _initializeWelcomeMessage() async {
    // Check if user is first time by looking at Firestore creation date
    final user = AuthService.currentUser;
    if (user?.uid != null) {
      try {
        final userDoc =
            await FirebaseFirestore.instance
                .collection('users')
                .doc(user!.uid)
                .get();

        if (userDoc.exists) {
          final data = userDoc.data();
          final createdAt = data?['createdAt'] as Timestamp?;
          final lastSignIn = data?['lastSignIn'] as Timestamp?;

          // Consider user as first time if account was created in the last 5 minutes
          if (createdAt != null && lastSignIn != null) {
            final now = DateTime.now();
            final createdTime = createdAt.toDate();
            final timeDifference = now.difference(createdTime).inMinutes;

            setState(() {
              _isFirstTimeUser = timeDifference <= 5;
              _showWelcome = true;
            });

            // Hide welcome message after 4 seconds
            Future.delayed(const Duration(seconds: 4), () {
              if (mounted) {
                setState(() {
                  _showWelcome = false;
                });
              }
            });
          }
        }
      } catch (e) {
        // If there's an error, default to showing welcome back
        setState(() {
          _isFirstTimeUser = false;
          _showWelcome = true;
        });

        Future.delayed(const Duration(seconds: 4), () {
          if (mounted) {
            setState(() {
              _showWelcome = false;
            });
          }
        });
      }
    }
  }

  /// Instagram-style initialization: Load cached data first, then refresh in background
  Future<void> _initializeHomeScreen() async {
    // Start location detection in background
    _getCurrentLocation();

    // Load cached data immediately
    await _loadCachedData();

    // Start background refresh
    _refreshInBackground();
  }

  /// Gets current location without blocking UI
  Future<void> _getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) return;

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) return;
      }

      if (permission == LocationPermission.deniedForever) return;

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Get address from coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      String city = '';
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        city = place.locality ??
            place.administrativeArea ??
            place.subAdministrativeArea ??
            '';
      }

      setState(() {
        _currentLatitude = position.latitude;
        _currentLongitude = position.longitude;
        _currentCity = city;
      });

      // Refresh data with new location
      _refreshInBackground();
    } catch (e) {
      // Handle error silently
    }
  }

  /// Loads cached data immediately
  Future<void> _loadCachedData() async {
    try {
      final cachedItems = await HomeCacheService.getItemsFromCache();
      if (cachedItems.isNotEmpty) {
        setState(() {
          _sortedItems = cachedItems;
          _hasInitialData = true;
        });
      }
    } catch (e) {
      // Handle error silently
    }
  }

  /// Refreshes data in background with loading indicator
  Future<void> _refreshInBackground() async {
    if (_isRefreshing) return;

    setState(() => _isRefreshing = true);

    try {
      final freshItems = await HomeCacheService.getItemsWithBackgroundRefresh(
        userLatitude: _currentLatitude,
        userLongitude: _currentLongitude,
        onFreshData: (items) {
          if (mounted) {
            setState(() {
              _sortedItems = items;
              _hasInitialData = true;
              _isRefreshing = false;
            });
          }
        },
      );

      // If no cached data was available, use fresh data immediately
      if (!_hasInitialData && freshItems.isNotEmpty) {
        setState(() {
          _sortedItems = freshItems;
          _hasInitialData = true;
        });
      }
    } catch (e) {
      // Handle error silently
    } finally {
      if (mounted) {
        setState(() => _isRefreshing = false);
      }
    }
  }

  /// Pull-to-refresh functionality
  Future<void> _onRefresh() async {
    try {
      final freshItems = await HomeCacheService.forceRefresh(
        userLatitude: _currentLatitude,
        userLongitude: _currentLongitude,
      );

      setState(() {
        _sortedItems = freshItems;
        _hasInitialData = true;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to refresh items'),
            backgroundColor: Color(0xFFE74C3C),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }





  Widget _buildItemsContent() {
    // Show loading only if no initial data
    if (!_hasInitialData && _sortedItems.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Color(0xFF3AD29F)),
            SizedBox(height: 16),
            Text(
              'Loading items...',
              style: TextStyle(
                fontSize: 16,
                color: Color(0xFF5A8A8A),
              ),
            ),
          ],
        ),
      );
    }

    // Show empty state
    if (_sortedItems.isEmpty) {
      return SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: SizedBox(
          height: MediaQuery.of(context).size.height * 0.5,
          child: Center(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.inventory_2_outlined,
                    size: 64,
                    color: Color(0xFF5A8A8A),
                  ),
                  SizedBox(height: 16),
                  Text(
                    'No Items Available',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2D5A5A),
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Be the first to list an item in your area!',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF5A8A8A),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }

    // Show items in grid
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 0.75, // Adjust for card height
      ),
      itemCount: _sortedItems.length,
      itemBuilder: (context, index) {
        final item = _sortedItems[index];
        return _buildGridItemCard(item);
      },
    );
  }

  String _getFirstName(String fullName) {
    // Extract first name from full name or email
    if (fullName.contains('@')) {
      // If it's an email, get the part before @
      fullName = fullName.split('@')[0];
    }

    // Get the first word (first name)
    final parts = fullName.split(' ');
    return parts.isNotEmpty ? parts[0] : 'User';
  }

  @override
  Widget build(BuildContext context) {
    final user = AuthService.currentUser;

    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        backgroundColor: AppTheme.getBackgroundColor(context),
        elevation: 0,
        title: Text(
          'EvenOut',
          style: TextStyle(
            color: AppTheme.getTextColor(context),
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
        actions: [
          if (_currentCity != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.location_on,
                    size: 16,
                    color: AppTheme.getPrimaryColor(context),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _currentCity!,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.getPrimaryColor(context),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
        ],
        actionsPadding: const EdgeInsets.only(right: 12),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome section (conditional)
            if (_showWelcome) ...[
              AnimatedContainer(
                duration: const Duration(milliseconds: 500),
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: AppTheme.getSecondaryColor(context),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _isFirstTimeUser
                          ? 'Welcome ${_getFirstName(user?.displayName ?? user?.email ?? 'User')}!'
                          : 'Welcome back ${_getFirstName(user?.displayName ?? user?.email ?? 'User')}!',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.getTextColor(context),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _isFirstTimeUser
                          ? 'Welcome to EvenOut! Ready to start exchanging goods and connecting with your community?'
                          : 'Ready to exchange goods and connect with your community?',
                      style: TextStyle(
                        fontSize: 16,
                        color: AppTheme.getSecondaryTextColor(context),
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),
            ],

            // Items grid with Instagram-style refresh
            Expanded(
              child: Stack(
                children: [
                  // Main content
                  RefreshIndicator(
                    onRefresh: _onRefresh,
                    color: const Color(0xFF3AD29F),
                    child: _buildItemsContent(),
                  ),

                  // Background refresh indicator (Instagram-style)
                  if (_isRefreshing && _hasInitialData)
                    Positioned(
                      top: 0,
                      left: 0,
                      right: 0,
                      child: SizedBox(
                        height: 3,
                        child: const LinearProgressIndicator(
                          backgroundColor: Colors.transparent,
                          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF3AD29F)),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGridItemCard(Map<String, dynamic> item) {
    final images = item['images'] as List<dynamic>? ?? [];
    final imageUrl = images.isNotEmpty ? images[0] as String : null;
    final distance = item['distance'] as double;
    final price = item['price'] as double?;


    return Container(
      decoration: BoxDecoration(
        color: AppTheme.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ItemDetailScreen(
                  itemId: item['docId'],
                  itemData: item,
                ),
              ),
            );
          },
          borderRadius: BorderRadius.circular(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Item Image
              Expanded(
                flex: 3,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                    color: AppTheme.getBackgroundColor(context),
                  ),
                  child: imageUrl != null
                      ? ClipRRect(
                          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                          child: Image.network(
                            imageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Center(
                                child: Icon(
                                  Icons.image_not_supported,
                                  size: 32,
                                  color: AppTheme.getSecondaryTextColor(context),
                                ),
                              );
                            },
                          ),
                        )
                      : Center(
                          child: Icon(
                            Icons.image,
                            size: 32,
                            color: AppTheme.getSecondaryTextColor(context),
                          ),
                        ),
                ),
              ),

              // Item Details
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Item Name
                      Text(
                        item['itemName'] ?? 'Unknown Item',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.getTextColor(context),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 4),

                      // Price
                      if (price != null)
                        Text(
                          '₹${price.toStringAsFixed(0)}',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.getPrimaryColor(context),
                          ),
                        )
                      else
                        Text(
                          'Free',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.getPrimaryColor(context),
                          ),
                        ),

                      const Spacer(),

                      // Distance and Category
                      Row(
                        children: [
                          if (distance != double.infinity) ...[
                            Expanded(
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: AppTheme.getPrimaryColor(context).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Text(
                                  distance < 1
                                      ? '${(distance * 1000).round()}m'
                                      : '${distance.toStringAsFixed(1)}km',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    color: AppTheme.getPrimaryColor(context),
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ] else ...[
                            Expanded(
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFE6C068).withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Text(
                                  item['category'] ?? 'Other',
                                  style: const TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFFE6C068),
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
