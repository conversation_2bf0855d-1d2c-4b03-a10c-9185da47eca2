import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/auth_service.dart';
import '../services/otp_service.dart';
import 'login_screen.dart';

class EditProfileScreen extends StatefulWidget {
  final VoidCallback? onProfileUpdated;

  const EditProfileScreen({
    super.key,
    this.onProfileUpdated,
  });

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _dobController = TextEditingController();
  final _cityController = TextEditingController();

  final ImagePicker _picker = ImagePicker();
  XFile? _selectedImage;
  String? _currentProfileImageUrl;

  bool _isLoading = false;
  bool _isLoadingData = true;
  bool _isLocationLoading = false;
  bool _isDeletingAccount = false;

  // Animation controllers
  late AnimationController _deleteIconController;
  late AnimationController _pulseController;
  late Animation<double> _deleteIconAnimation;
  late Animation<double> _pulseAnimation;

  // User data that cannot be changed
  String? _displayName;
  String? _email;
  String? _phoneNumber;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadUserData();
  }

  void _initializeAnimations() {
    // Delete icon shake animation
    _deleteIconController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _deleteIconAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _deleteIconController,
      curve: Curves.elasticOut,
    ));

    // Pulse animation for attention
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Start continuous pulse animation
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _dobController.dispose();
    _cityController.dispose();
    _deleteIconController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    try {
      final user = AuthService.currentUser;
      if (user == null) return;

      final userData = await AuthService.getUserData(user.uid);

      if (userData != null && mounted) {
        setState(() {
          _displayName = userData['displayName'] ?? user.displayName;
          _email = userData['email'] ?? user.email;
          _phoneNumber = userData['phoneNumber'];
          _currentProfileImageUrl = userData['profileImageUrl'];
          _dobController.text = userData['dateOfBirth'] ?? '';
          _cityController.text = userData['city'] ?? '';
          _isLoadingData = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoadingData = false);
        _showErrorSnackBar('Failed to load user data: ${e.toString()}');
      }
    }
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = image;
        });
      }
    } catch (e) {
      _showErrorSnackBar('Failed to pick image: ${e.toString()}');
    }
  }

  Future<void> _takePhoto() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = image;
        });
      }
    } catch (e) {
      _showErrorSnackBar('Failed to take photo: ${e.toString()}');
    }
  }

  Future<String?> _uploadProfileImage() async {
    if (_selectedImage == null) return _currentProfileImageUrl;

    try {
      final user = AuthService.currentUser;
      if (user == null) throw 'User not authenticated';

      // Create a reference to Firebase Storage
      final storageRef = FirebaseStorage.instance
          .ref()
          .child('profile_images')
          .child('${user.uid}.jpg');

      // Upload the file
      final uploadTask = storageRef.putFile(File(_selectedImage!.path));
      final snapshot = await uploadTask;

      // Get the download URL
      final downloadUrl = await snapshot.ref.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      throw 'Failed to upload profile image: $e';
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final user = AuthService.currentUser;
      if (user == null) throw 'User not authenticated';

      // Upload profile image if selected
      final profileImageUrl = await _uploadProfileImage();

      // Prepare update data
      final updateData = <String, dynamic>{};

      if (_dobController.text.trim().isNotEmpty) {
        updateData['dateOfBirth'] = _dobController.text.trim();
      }

      if (_cityController.text.trim().isNotEmpty) {
        updateData['city'] = _cityController.text.trim();
      }

      if (profileImageUrl != null) {
        updateData['profileImageUrl'] = profileImageUrl;
      }

      // Update user data using AuthService
      await AuthService.updateUserData(user.uid, updateData);

      if (mounted) {
        Navigator.of(context).pop();

        // Call the callback to notify parent widget
        widget.onProfileUpdated?.call();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile updated successfully!'),
            backgroundColor: Color(0xFF3AD29F),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Failed to update profile: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFFE74C3C),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 6570)), // 18 years ago
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF3AD29F),
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Color(0xFF2D5A5A),
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _dobController.text = '${picked.day}/${picked.month}/${picked.year}';
      });
    }
  }

  Future<void> _getCurrentLocation() async {
    setState(() => _isLocationLoading = true);

    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _showErrorSnackBar('Location services are disabled. Please enable them in settings.');
        return;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _showErrorSnackBar('Location permissions are denied.');
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _showErrorSnackBar('Location permissions are permanently denied. Please enable them in settings.');
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Get address from coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        String city = place.locality ?? place.administrativeArea ?? place.subAdministrativeArea ?? '';

        if (city.isNotEmpty) {
          setState(() {
            _cityController.text = city;
          });
        } else {
          _showErrorSnackBar('Could not determine city from your location.');
        }
      } else {
        _showErrorSnackBar('Could not get location details.');
      }
    } catch (e) {
      _showErrorSnackBar('Failed to get location: ${e.toString()}');
    } finally {
      setState(() => _isLocationLoading = false);
    }
  }

  Future<void> _showDeleteAccountDialog() async {
    // Trigger shake animation
    _deleteIconController.forward().then((_) {
      _deleteIconController.reset();
    });

    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: Container(
            height: MediaQuery.of(context).size.height * 0.7,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
            ),
            child: Column(
              children: [
                // Handle bar
                Container(
                  margin: const EdgeInsets.only(top: 12),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      children: [
                        // Animated warning icon
                        AnimatedBuilder(
                          animation: _deleteIconAnimation,
                          builder: (context, child) {
                            return Transform.translate(
                              offset: Offset(
                                10 * _deleteIconAnimation.value *
                                (1 - _deleteIconAnimation.value) *
                                4 * (0.5 - (_deleteIconAnimation.value - 0.5).abs()),
                                0,
                              ),
                              child: Container(
                                width: 80,
                                height: 80,
                                decoration: BoxDecoration(
                                  color: const Color(0xFFE74C3C).withValues(alpha: 0.1),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.warning_rounded,
                                  color: Color(0xFFE74C3C),
                                  size: 40,
                                ),
                              ),
                            );
                          },
                        ),

                        const SizedBox(height: 24),

                        // Title
                        const Text(
                          'Delete Account',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFFE74C3C),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Warning message
                        const Text(
                          'Are you sure you want to delete your account?',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF2D5A5A),
                          ),
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 24),

                        // What will be deleted
                        Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: const Color(0xFFE74C3C).withValues(alpha: 0.05),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: const Color(0xFFE74C3C).withValues(alpha: 0.2),
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'This action will permanently delete:',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF2D5A5A),
                                ),
                              ),
                              const SizedBox(height: 12),
                              _buildDeleteItem('All your profile data'),
                              _buildDeleteItem('All your listed items'),
                              _buildDeleteItem('All your account details'),
                              _buildDeleteItem('All uploaded images'),
                            ],
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Warning text
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: const Color(0xFFE74C3C).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Row(
                            children: [
                              Icon(
                                Icons.info_outline,
                                color: Color(0xFFE74C3C),
                                size: 20,
                              ),
                              SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  'This action cannot be undone.',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFFE74C3C),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        const Spacer(),

                        // Action buttons
                        Row(
                          children: [
                            Expanded(
                              child: TextButton(
                                onPressed: () => Navigator.of(context).pop(),
                                style: TextButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    side: const BorderSide(color: Color(0xFF5A8A8A)),
                                  ),
                                ),
                                child: const Text(
                                  'Cancel',
                                  style: TextStyle(
                                    color: Color(0xFF5A8A8A),
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: ElevatedButton(
                                onPressed: () async {
                                  Navigator.of(context).pop();
                                  await _sendAccountDeletionOTP();
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFFE74C3C),
                                  padding: const EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: const Text(
                                  'Continue',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDeleteItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          const Icon(
            Icons.close,
            color: Color(0xFFE74C3C),
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF5A8A8A),
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _sendAccountDeletionOTP() async {
    try {
      final user = AuthService.currentUser;
      if (user?.email == null) {
        _showErrorSnackBar('No email found for your account');
        return;
      }

      // Send account deletion OTP
      await OTPService.sendAccountDeletionOTP(
        email: user!.email!,
        userName: _displayName,
      );

      // Show OTP verification modal
      _showOTPVerificationModal();

    } catch (e) {
      _showErrorSnackBar('Failed to send OTP: ${e.toString().replaceFirst('Exception: ', '')}');
    }
  }

  Future<void> _showOTPVerificationModal() async {
    final otpController = TextEditingController();
    final passwordController = TextEditingController();
    bool isPasswordVisible = false;
    bool isVerifying = false;
    final user = AuthService.currentUser;

    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.8,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
              ),
              child: Padding(
                padding: EdgeInsets.only(
                  left: 24,
                  right: 24,
                  top: 24,
                  bottom: MediaQuery.of(context).viewInsets.bottom + 24,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: const Color(0xFFE74C3C).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.security,
                            color: Color(0xFFE74C3C),
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        const Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Account Deletion Verification',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF2D5A5A),
                                ),
                              ),
                              Text(
                                'Double security verification required',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Color(0xFF5A8A8A),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // OTP Section
                    const Text(
                      'Step 1: Enter OTP',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF2D5A5A),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'We sent a verification code to ${user?.email}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF5A8A8A),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // OTP Input
                    TextFormField(
                      controller: otpController,
                      keyboardType: TextInputType.number,
                      maxLength: 6,
                      decoration: InputDecoration(
                        labelText: '6-digit OTP',
                        hintText: 'Enter OTP from email',
                        prefixIcon: const Icon(Icons.security, color: Color(0xFF5A8A8A)),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: Color(0xFF3AD29F), width: 2),
                        ),
                        counterText: '',
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Password Section
                    const Text(
                      'Step 2: Confirm Password',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF2D5A5A),
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Enter your account password to confirm deletion',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF5A8A8A),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Password Input
                    TextFormField(
                      controller: passwordController,
                      obscureText: !isPasswordVisible,
                      decoration: InputDecoration(
                        labelText: 'Password',
                        hintText: 'Enter your password',
                        prefixIcon: const Icon(Icons.lock_outline, color: Color(0xFF5A8A8A)),
                        suffixIcon: IconButton(
                          icon: Icon(
                            isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                            color: const Color(0xFF5A8A8A),
                          ),
                          onPressed: () {
                            setModalState(() {
                              isPasswordVisible = !isPasswordVisible;
                            });
                          },
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: Color(0xFF3AD29F), width: 2),
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Warning
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: const Color(0xFFE74C3C).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Row(
                        children: [
                          Icon(
                            Icons.warning_amber_outlined,
                            color: Color(0xFFE74C3C),
                            size: 20,
                          ),
                          SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'This action cannot be undone. All your data will be permanently deleted.',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Color(0xFFE74C3C),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const Spacer(),

                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: isVerifying ? null : () {
                              Navigator.of(context).pop();
                            },
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                                side: const BorderSide(color: Color(0xFF5A8A8A)),
                              ),
                            ),
                            child: const Text(
                              'Cancel',
                              style: TextStyle(
                                color: Color(0xFF5A8A8A),
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: (isVerifying ||
                                       otpController.text.trim().length != 6 ||
                                       passwordController.text.trim().isEmpty)
                                ? null
                                : () async {
                              setModalState(() {
                                isVerifying = true;
                              });

                              try {
                                // First verify OTP
                                await OTPService.verifyAccountDeletionOTP(
                                  email: user!.email!,
                                  otp: otpController.text.trim(),
                                );

                                // Then authenticate and delete account
                                await _authenticateAndDeleteAccount(passwordController.text.trim());

                                if (context.mounted) {
                                  Navigator.of(context).pop(); // Close OTP modal
                                }
                              } catch (e) {
                                setModalState(() {
                                  isVerifying = false;
                                });
                                _showErrorSnackBar(e.toString().replaceFirst('Exception: ', ''));
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFFE74C3C),
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: isVerifying
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: Colors.white,
                                    ),
                                  )
                                : const Text(
                                    'Delete Account',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  // Old password confirmation modal removed - now using OTP + password verification

  Future<void> _authenticateAndDeleteAccount(String password) async {
    setState(() => _isDeletingAccount = true);

    try {
      final user = AuthService.currentUser;
      if (user == null) {
        throw 'No user found to delete.';
      }

      // Step 1: Re-authenticate user with password
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: password,
      );

      await user.reauthenticateWithCredential(credential);

      // Show loading dialog
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(color: Color(0xFFE74C3C)),
                SizedBox(height: 16),
                Text(
                  'Deleting your account...',
                  style: TextStyle(
                    fontSize: 16,
                    color: Color(0xFF2D5A5A),
                  ),
                ),
              ],
            ),
          ),
        );
      }

      // Step 2: Delete user data from Firestore
      await _deleteUserDataFromFirestore(user.uid);

      // Step 3: Delete user files from Storage
      await _deleteUserFilesFromStorage(user.uid);

      // Step 4: Delete user from Authentication (this must be last)
      await user.delete();

      // Navigate to login screen
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (_) => const LoginScreen()),
          (route) => false,
        );
      }
    } on FirebaseAuthException catch (e) {
      String errorMessage;
      switch (e.code) {
        case 'wrong-password':
          errorMessage = 'Incorrect password. Please try again.';
          break;
        case 'too-many-requests':
          errorMessage = 'Too many failed attempts. Please try again later.';
          break;
        case 'user-disabled':
          errorMessage = 'This account has been disabled.';
          break;
        case 'user-not-found':
          errorMessage = 'Account not found.';
          break;
        case 'network-request-failed':
          errorMessage = 'Network error. Please check your connection.';
          break;
        default:
          errorMessage = 'Authentication failed: ${e.message}';
      }

      if (mounted) {
        Navigator.of(context).pop();
      }
      throw errorMessage;
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop();
      }
      throw 'Failed to delete account: ${e.toString()}';
    } finally {
      if (mounted) {
        setState(() => _isDeletingAccount = false);
      }
    }
  }

  Future<void> _deleteUserDataFromFirestore(String uid) async {
    final firestore = FirebaseFirestore.instance;

    try {
      // Delete user document
      await firestore.collection('users').doc(uid).delete();

      // Delete user's items in batches (Firestore batch limit is 500 operations)
      bool hasMoreItems = true;
      while (hasMoreItems) {
        final itemsQuery = await firestore
            .collection('items')
            .where('userId', isEqualTo: uid)
            .limit(500)
            .get();

        if (itemsQuery.docs.isEmpty) {
          hasMoreItems = false;
          break;
        }

        final batch = firestore.batch();
        for (var doc in itemsQuery.docs) {
          batch.delete(doc.reference);
        }
        await batch.commit();

        // If we got less than 500 documents, we're done
        if (itemsQuery.docs.length < 500) {
          hasMoreItems = false;
        }
      }

      // Delete any other user-related collections
      // Add more collections here if needed in the future

    } catch (e) {
      throw 'Failed to delete user data from Firestore: $e';
    }
  }

  Future<void> _deleteUserFilesFromStorage(String uid) async {
    final storage = FirebaseStorage.instance;
    final List<String> deletionErrors = [];

    try {
      // Delete profile images
      try {
        await storage.ref().child('profile_images/$uid.jpg').delete();
      } catch (e) {
        debugPrint('Profile image deletion failed: $e');
      }

      // Delete item images directory
      try {
        final itemImagesRef = storage.ref().child('item_images/$uid');
        final listResult = await itemImagesRef.listAll();
        for (var item in listResult.items) {
          try {
            await item.delete();
          } catch (e) {
            deletionErrors.add('Failed to delete ${item.fullPath}: $e');
          }
        }
        for (var prefix in listResult.prefixes) {
          try {
            final subListResult = await prefix.listAll();
            for (var item in subListResult.items) {
              try {
                await item.delete();
              } catch (e) {
                deletionErrors.add('Failed to delete ${item.fullPath}: $e');
              }
            }
          } catch (e) {
            deletionErrors.add('Failed to list subdirectory ${prefix.fullPath}: $e');
          }
        }
      } catch (e) {
        debugPrint('Item images directory deletion failed: $e');
      }

      // Delete all files in documents/uid/
      try {
        final documentsRef = storage.ref().child('documents/$uid');
        final listResult = await documentsRef.listAll();
        for (var item in listResult.items) {
          try {
            await item.delete();
          } catch (e) {
            deletionErrors.add('Failed to delete ${item.fullPath}: $e');
          }
        }
        for (var prefix in listResult.prefixes) {
          try {
            final subListResult = await prefix.listAll();
            for (var item in subListResult.items) {
              try {
                await item.delete();
              } catch (e) {
                deletionErrors.add('Failed to delete ${item.fullPath}: $e');
              }
            }
          } catch (e) {
            deletionErrors.add('Failed to list subdirectory ${prefix.fullPath}: $e');
          }
        }
      } catch (e) {
        debugPrint('Document images directory deletion failed: $e');
      }

      if (deletionErrors.isNotEmpty) {
        debugPrint('Some files could not be deleted: ${deletionErrors.join(', ')}');
      }

    } catch (e) {
      debugPrint('Storage deletion encountered errors: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoadingData) {
      return Scaffold(
        backgroundColor: const Color(0xFFF5F1E8),
        appBar: AppBar(
          backgroundColor: const Color(0xFFF5F1E8),
          elevation: 0,
          leading: IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.arrow_back,
              color: Color(0xFF2D5A5A),
            ),
          ),
          title: const Text(
            'Edit Profile',
            style: TextStyle(
              color: Color(0xFF2D5A5A),
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
        ),
        body: const Center(
          child: CircularProgressIndicator(
            color: Color(0xFF3AD29F),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF5F1E8),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF5F1E8),
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back,
            color: Color(0xFF2D5A5A),
          ),
        ),
        title: const Text(
          'Edit Profile',
          style: TextStyle(
            color: Color(0xFF2D5A5A),
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProfile,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Color(0xFF3AD29F),
                    ),
                  )
                : const Text(
                    'Save',
                    style: TextStyle(
                      color: Color(0xFF3AD29F),
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile Picture Section
              _buildProfilePictureSection(),
              const SizedBox(height: 32),

              // Non-editable fields
              _buildNonEditableSection(),
              const SizedBox(height: 32),

              // Editable fields
              _buildEditableSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfilePictureSection() {
    return Center(
      child: Column(
        children: [
          const Text(
            'Profile Picture',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2D5A5A),
            ),
          ),
          const SizedBox(height: 16),

          Stack(
            children: [
              CircleAvatar(
                radius: 60,
                backgroundColor: const Color(0xFF2D5A5A),
                backgroundImage: _selectedImage != null
                    ? FileImage(File(_selectedImage!.path))
                    : (_currentProfileImageUrl != null
                        ? NetworkImage(_currentProfileImageUrl!)
                        : null),
                child: (_selectedImage == null && _currentProfileImageUrl == null)
                    ? const Icon(
                        Icons.person,
                        size: 60,
                        color: Colors.white,
                      )
                    : null,
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  decoration: const BoxDecoration(
                    color: Color(0xFF3AD29F),
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    onPressed: _showImagePickerOptions,
                    icon: const Icon(
                      Icons.camera_alt,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),
          const Text(
            'Tap the camera icon to change your profile picture',
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFF5A8A8A),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showImagePickerOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Select Profile Picture',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2D5A5A),
              ),
            ),
            const SizedBox(height: 24),

            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _pickImage();
                    },
                    icon: const Icon(Icons.photo_library, color: Colors.white),
                    label: const Text(
                      'Gallery',
                      style: TextStyle(color: Colors.white),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF5A8A8A),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _takePhoto();
                    },
                    icon: const Icon(Icons.camera_alt, color: Colors.white),
                    label: const Text(
                      'Camera',
                      style: TextStyle(color: Colors.white),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF3AD29F),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildNonEditableSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Account Information',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2D5A5A),
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'These fields cannot be changed',
          style: TextStyle(
            fontSize: 14,
            color: Color(0xFF5A8A8A),
          ),
        ),
        const SizedBox(height: 16),

        _buildReadOnlyField(
          label: 'Full Name',
          value: _displayName ?? 'Not set',
          icon: Icons.person,
        ),

        const SizedBox(height: 16),

        _buildReadOnlyField(
          label: 'Email Address',
          value: _email ?? 'Not set',
          icon: Icons.email,
        ),

        const SizedBox(height: 16),

        _buildReadOnlyField(
          label: 'Phone Number',
          value: _phoneNumber ?? 'Not verified',
          icon: Icons.phone,
        ),
      ],
    );
  }

  Widget _buildReadOnlyField({
    required String label,
    required String value,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE0E0E0)),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: const Color(0xFF5A8A8A),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF5A8A8A),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Color(0xFF2D5A5A),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditableSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Personal Information',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2D5A5A),
          ),
        ),
        const SizedBox(height: 16),

        // Date of Birth
        const Text(
          'Date of Birth',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2D5A5A),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _dobController,
          readOnly: true,
          onTap: _selectDate,
          style: const TextStyle(
            color: Color(0xFF2D5A5A),
            fontSize: 16,
          ),
          decoration: InputDecoration(
            hintText: 'Select your date of birth',
            hintStyle: const TextStyle(color: Color(0xFF5A8A8A)),
            suffixIcon: const Icon(
              Icons.calendar_today,
              color: Color(0xFF5A8A8A),
            ),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF3AD29F), width: 2),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),

        const SizedBox(height: 24),

        // City
        const Text(
          'City',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2D5A5A),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _cityController,
          style: const TextStyle(
            color: Color(0xFF2D5A5A),
            fontSize: 16,
          ),
          decoration: InputDecoration(
            hintText: 'Enter your city',
            hintStyle: const TextStyle(color: Color(0xFF5A8A8A)),
            suffixIcon: _isLocationLoading
                ? const Padding(
                    padding: EdgeInsets.all(12),
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Color(0xFF3AD29F),
                      ),
                    ),
                  )
                : IconButton(
                    onPressed: _getCurrentLocation,
                    icon: const Icon(
                      Icons.my_location,
                      color: Color(0xFF3AD29F),
                    ),
                    tooltip: 'Use current location',
                  ),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF3AD29F), width: 2),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
          validator: (value) {
            if (value?.trim().isNotEmpty == true && value!.trim().length < 2) {
              return 'City name should be at least 2 characters';
            }
            return null;
          },
        ),

        const SizedBox(height: 40),

        // Delete Account Section
        _buildDeleteAccountSection(),

        const SizedBox(height: 40),
      ],
    );
  }

  Widget _buildDeleteAccountSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Danger Zone',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFFE74C3C),
          ),
        ),
        const SizedBox(height: 16),

        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFFE74C3C).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFFE74C3C).withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _pulseAnimation.value,
                        child: const Icon(
                          Icons.warning_outlined,
                          color: Color(0xFFE74C3C),
                          size: 24,
                        ),
                      );
                    },
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'Delete Account',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFFE74C3C),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              const Text(
                'Permanently delete your account and all associated data. This action cannot be undone.',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF5A8A8A),
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isDeletingAccount ? null : () {
                    // Trigger delete icon animation
                    _deleteIconController.forward().then((_) {
                      _deleteIconController.reset();
                    });
                    _showDeleteAccountDialog();
                  },
                  icon: _isDeletingAccount
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : AnimatedBuilder(
                          animation: _deleteIconAnimation,
                          builder: (context, child) {
                            return Transform.rotate(
                              angle: _deleteIconAnimation.value * 0.1,
                              child: Transform.scale(
                                scale: 1.0 + (_deleteIconAnimation.value * 0.1),
                                child: const Icon(
                                  Icons.delete_forever,
                                  color: Colors.white,
                                ),
                              ),
                            );
                          },
                        ),
                  label: Text(
                    _isDeletingAccount ? 'Deleting...' : 'Delete Account',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFE74C3C),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}