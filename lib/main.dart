import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart' as provider;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:background_location_tracker/background_location_tracker.dart';
import 'firebase_options.dart';
import 'screens/splash_screen.dart';
import 'providers/theme_provider.dart';
import 'themes/app_theme.dart';
import 'services/location_service.dart';

@pragma('vm:entry-point')
void backgroundLocationCallback() {
  BackgroundLocationTrackerManager.handleBackgroundUpdated(
    (data) async {
      await LocationUpdateHandler.handleLocationUpdate(data);
    },
  );
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Initialize background location tracker
  await BackgroundLocationTrackerManager.initialize(
    backgroundLocationCallback,
    config: BackgroundLocationTrackerConfig(
      loggingEnabled: false,
      androidConfig: AndroidConfig(
        notificationIcon: 'ic_launcher',
        trackingInterval: Duration(seconds: 30),
        distanceFilterMeters: 50,
      ),
      iOSConfig: IOSConfig(
        activityType: ActivityType.OTHER,
        distanceFilterMeters: 50,
        restartAfterKill: true,
      ),
    ),
  );

  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return provider.ChangeNotifierProvider(
      create: (context) => ThemeProvider(),
      child: provider.Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'EvenOut',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeProvider.isDarkTheme
                ? ThemeMode.dark
                : ThemeMode.light,
            home: const SplashScreen(),
          );
        },
      ),
    );
  }
}
