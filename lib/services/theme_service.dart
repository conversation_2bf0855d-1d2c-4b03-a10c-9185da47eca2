import 'package:shared_preferences/shared_preferences.dart';

class ThemeService {
  static const String _themeKey = 'app_theme_mode';
  static const String _lightTheme = 'light';
  static const String _darkTheme = 'dark';

  /// Gets the saved theme mode from SharedPreferences
  /// Returns true for dark theme, false for light theme
  /// Defaults to light theme if no preference is saved
  static Future<bool> isDarkTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeMode = prefs.getString(_themeKey) ?? _lightTheme;
      return themeMode == _darkTheme;
    } catch (e) {
      print('Error getting theme preference: $e');
      return false; // Default to light theme
    }
  }

  /// Saves the theme mode to SharedPreferences
  /// [isDark] - true for dark theme, false for light theme
  static Future<void> setTheme(bool isDark) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeMode = isDark ? _darkTheme : _lightTheme;
      await prefs.setString(_themeKey, themeMode);
    } catch (e) {
      print('Error saving theme preference: $e');
    }
  }

  /// Toggles the current theme and saves it
  /// Returns the new theme state (true for dark, false for light)
  static Future<bool> toggleTheme() async {
    try {
      final currentIsDark = await isDarkTheme();
      final newIsDark = !currentIsDark;
      await setTheme(newIsDark);
      return newIsDark;
    } catch (e) {
      print('Error toggling theme: $e');
      return false;
    }
  }
}
