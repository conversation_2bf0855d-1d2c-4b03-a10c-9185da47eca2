import 'dart:async';
import 'package:background_location_tracker/background_location_tracker.dart';
import 'package:geolocator/geolocator.dart' hide ActivityType;
import 'package:geocoding/geocoding.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  final StreamController<LocationData> _locationController = StreamController<LocationData>.broadcast();
  Stream<LocationData> get locationStream => _locationController.stream;

  LocationData? _currentLocation;
  LocationData? get currentLocation => _currentLocation;

  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  bool _isTracking = false;
  bool get isTracking => _isTracking;

  /// Initialize background location tracking
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Request permissions first
      await _requestPermissions();

      // Initialize the background location tracker
      await BackgroundLocationTrackerManager.initialize(
        backgroundLocationCallback,
        config: BackgroundLocationTrackerConfig(
          loggingEnabled: false, // Set to true for debugging
          androidConfig: AndroidConfig(
            notificationIcon: 'ic_launcher',
            trackingInterval: Duration(seconds: 30), // Update every 30 seconds
            distanceFilterMeters: 50, // Update every 50 meters
          ),
          iOSConfig: IOSConfig(
            activityType: ActivityType.OTHER,
            distanceFilterMeters: 50, // Update every 50 meters
            restartAfterKill: true,
          ),
        ),
      );

      // Get initial location
      await _getCurrentLocation();

      _isInitialized = true;
      print('LocationService: Initialized successfully');
    } catch (e) {
      print('LocationService: Failed to initialize - $e');
      throw Exception('Failed to initialize location service: $e');
    }
  }

  /// Request location permissions
  Future<void> _requestPermissions() async {
    LocationPermission permission = await Geolocator.checkPermission();

    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
    }

    if (permission == LocationPermission.deniedForever) {
      throw Exception('Location permissions are permanently denied');
    }

    if (permission == LocationPermission.denied) {
      throw Exception('Location permissions denied');
    }
  }

  /// Get current location immediately
  Future<LocationData?> _getCurrentLocation() async {
    try {
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      final locationData = await _createLocationData(position.latitude, position.longitude);
      _updateLocation(locationData);
      return locationData;
    } catch (e) {
      print('LocationService: Failed to get current location - $e');
      return null;
    }
  }

  /// Create LocationData from coordinates
  Future<LocationData> _createLocationData(double latitude, double longitude) async {
    String? city;
    try {
      final placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        city = placemark.locality ?? placemark.subAdministrativeArea ?? placemark.administrativeArea;
      }
    } catch (e) {
      print('LocationService: Failed to get city name - $e');
    }

    return LocationData(
      latitude: latitude,
      longitude: longitude,
      city: city,
      timestamp: DateTime.now(),
    );
  }

  /// Update location and notify listeners
  void _updateLocation(LocationData locationData) {
    _currentLocation = locationData;
    _locationController.add(locationData);
    _saveLocationToPrefs(locationData);
  }

  /// Save location to SharedPreferences
  Future<void> _saveLocationToPrefs(LocationData location) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('last_latitude', location.latitude);
      await prefs.setDouble('last_longitude', location.longitude);
      await prefs.setString('last_city', location.city ?? '');
      await prefs.setInt('last_location_timestamp', location.timestamp.millisecondsSinceEpoch);
    } catch (e) {
      print('LocationService: Failed to save location to prefs - $e');
    }
  }

  /// Load location from SharedPreferences
  Future<LocationData?> loadLocationFromPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final latitude = prefs.getDouble('last_latitude');
      final longitude = prefs.getDouble('last_longitude');
      final city = prefs.getString('last_city');
      final timestamp = prefs.getInt('last_location_timestamp');

      if (latitude != null && longitude != null && timestamp != null) {
        final locationData = LocationData(
          latitude: latitude,
          longitude: longitude,
          city: city?.isEmpty == true ? null : city,
          timestamp: DateTime.fromMillisecondsSinceEpoch(timestamp),
        );
        _currentLocation = locationData;
        return locationData;
      }
    } catch (e) {
      print('LocationService: Failed to load location from prefs - $e');
    }
    return null;
  }

  /// Start location tracking
  Future<void> startTracking() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      await BackgroundLocationTrackerManager.startTracking();
      _isTracking = true;
      print('LocationService: Started tracking');
    } catch (e) {
      print('LocationService: Failed to start tracking - $e');
    }
  }

  /// Stop location tracking
  Future<void> stopTracking() async {
    try {
      await BackgroundLocationTrackerManager.stopTracking();
      _isTracking = false;
      print('LocationService: Stopped tracking');
    } catch (e) {
      print('LocationService: Failed to stop tracking - $e');
    }
  }

  /// Check if currently tracking
  Future<bool> checkTrackingStatus() async {
    try {
      _isTracking = await BackgroundLocationTrackerManager.isTracking();
      return _isTracking;
    } catch (e) {
      print('LocationService: Failed to check tracking status - $e');
      return false;
    }
  }

  /// Calculate distance between two points in kilometers
  static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    return Geolocator.distanceBetween(lat1, lon1, lat2, lon2) / 1000;
  }

  /// Dispose resources
  void dispose() {
    _locationController.close();
  }
}

/// Background location callback - must be top-level function
@pragma('vm:entry-point')
void backgroundLocationCallback() {
  BackgroundLocationTrackerManager.handleBackgroundUpdated(
    (data) async {
      // Handle background location update
      await LocationUpdateHandler.handleLocationUpdate(data);
    },
  );
}

/// Helper class to handle background location updates
class LocationUpdateHandler {
  static Future<void> handleLocationUpdate(BackgroundLocationUpdateData data) async {
    try {
      // Create location data
      String? city;
      try {
        final placemarks = await placemarkFromCoordinates(data.lat, data.lon);
        if (placemarks.isNotEmpty) {
          final placemark = placemarks.first;
          city = placemark.locality ?? placemark.subAdministrativeArea ?? placemark.administrativeArea;
        }
      } catch (e) {
        print('LocationUpdateHandler: Failed to get city name - $e');
      }

      final locationData = LocationData(
        latitude: data.lat,
        longitude: data.lon,
        city: city,
        timestamp: DateTime.now(),
      );

      // Save to preferences
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setDouble('last_latitude', locationData.latitude);
        await prefs.setDouble('last_longitude', locationData.longitude);
        await prefs.setString('last_city', locationData.city ?? '');
        await prefs.setInt('last_location_timestamp', locationData.timestamp.millisecondsSinceEpoch);

        // Set a flag to indicate new location is available
        await prefs.setBool('has_new_location', true);
      } catch (e) {
        print('LocationUpdateHandler: Failed to save location - $e');
      }

      print('LocationUpdateHandler: Background location updated - ${locationData.city}');
    } catch (e) {
      print('LocationUpdateHandler: Failed to handle location update - $e');
    }
  }
}

/// Location data model
class LocationData {
  final double latitude;
  final double longitude;
  final String? city;
  final DateTime timestamp;

  const LocationData({
    required this.latitude,
    required this.longitude,
    this.city,
    required this.timestamp,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationData &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.city == city;
  }

  @override
  int get hashCode {
    return latitude.hashCode ^ longitude.hashCode ^ city.hashCode;
  }

  @override
  String toString() {
    return 'LocationData(lat: $latitude, lng: $longitude, city: $city, time: $timestamp)';
  }
}
