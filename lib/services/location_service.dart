import 'dart:async';
import 'package:flutter_background_geolocation/flutter_background_geolocation.dart' as bg;
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  final StreamController<LocationData> _locationController = StreamController<LocationData>.broadcast();
  Stream<LocationData> get locationStream => _locationController.stream;

  LocationData? _currentLocation;
  LocationData? get currentLocation => _currentLocation;

  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  /// Initialize background geolocation
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Request permissions first
      await _requestPermissions();

      // Configure background geolocation
      await bg.BackgroundGeolocation.ready(bg.Config(
        // Geolocation Config
        desiredAccuracy: bg.Config.DESIRED_ACCURACY_HIGH,
        distanceFilter: 50.0, // Update every 50 meters
        stopTimeout: 1,

        // Activity Recognition
        stopDetectionDelay: 1,
        heartbeatInterval: 60,

        // Application config
        debug: false, // Set to true for debugging
        logLevel: bg.Config.LOG_LEVEL_OFF,
        enableHeadless: true,

        // HTTP / Persistence config
        autoSync: false,
        maxDaysToPersist: 1,

        // Geofencing config
        geofenceProximityRadius: 1000,

        // iOS specific
        preventSuspend: false,
        locationAuthorizationRequest: 'WhenInUse',

        // Android specific
        notification: bg.Notification(
          title: "EvenOut Location",
          text: "Tracking location for nearby items",
          color: "#3AD29F",
          channelName: "Location Updates",
          smallIcon: "drawable/ic_launcher",
          largeIcon: "drawable/ic_launcher",
        ),
        foregroundService: true,
      ));

      // Listen to location events
      bg.BackgroundGeolocation.onLocation(_onLocationUpdate);
      bg.BackgroundGeolocation.onMotionChange(_onMotionChange);
      bg.BackgroundGeolocation.onActivityChange(_onActivityChange);
      bg.BackgroundGeolocation.onProviderChange(_onProviderChange);

      // Get initial location
      await _getCurrentLocation();

      _isInitialized = true;
      print('LocationService: Initialized successfully');
    } catch (e) {
      print('LocationService: Failed to initialize - $e');
      throw Exception('Failed to initialize location service: $e');
    }
  }

  /// Request location permissions
  Future<void> _requestPermissions() async {
    LocationPermission permission = await Geolocator.checkPermission();

    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
    }

    if (permission == LocationPermission.deniedForever) {
      throw Exception('Location permissions are permanently denied');
    }

    if (permission == LocationPermission.denied) {
      throw Exception('Location permissions denied');
    }
  }

  /// Get current location immediately
  Future<LocationData?> _getCurrentLocation() async {
    try {
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      final locationData = await _createLocationData(position.latitude, position.longitude);
      _updateLocation(locationData);
      return locationData;
    } catch (e) {
      print('LocationService: Failed to get current location - $e');
      return null;
    }
  }

  /// Create LocationData from coordinates
  Future<LocationData> _createLocationData(double latitude, double longitude) async {
    String? city;
    try {
      final placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        city = placemark.locality ?? placemark.subAdministrativeArea ?? placemark.administrativeArea;
      }
    } catch (e) {
      print('LocationService: Failed to get city name - $e');
    }

    return LocationData(
      latitude: latitude,
      longitude: longitude,
      city: city,
      timestamp: DateTime.now(),
    );
  }

  /// Update location and notify listeners
  void _updateLocation(LocationData locationData) {
    _currentLocation = locationData;
    _locationController.add(locationData);
    _saveLocationToPrefs(locationData);
  }

  /// Save location to SharedPreferences
  Future<void> _saveLocationToPrefs(LocationData location) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('last_latitude', location.latitude);
      await prefs.setDouble('last_longitude', location.longitude);
      await prefs.setString('last_city', location.city ?? '');
      await prefs.setInt('last_location_timestamp', location.timestamp.millisecondsSinceEpoch);
    } catch (e) {
      print('LocationService: Failed to save location to prefs - $e');
    }
  }

  /// Load location from SharedPreferences
  Future<LocationData?> loadLocationFromPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final latitude = prefs.getDouble('last_latitude');
      final longitude = prefs.getDouble('last_longitude');
      final city = prefs.getString('last_city');
      final timestamp = prefs.getInt('last_location_timestamp');

      if (latitude != null && longitude != null && timestamp != null) {
        final locationData = LocationData(
          latitude: latitude,
          longitude: longitude,
          city: city?.isEmpty == true ? null : city,
          timestamp: DateTime.fromMillisecondsSinceEpoch(timestamp),
        );
        _currentLocation = locationData;
        return locationData;
      }
    } catch (e) {
      print('LocationService: Failed to load location from prefs - $e');
    }
    return null;
  }

  /// Background location update handler
  void _onLocationUpdate(bg.Location location) async {
    try {
      final locationData = await _createLocationData(
        location.coords.latitude,
        location.coords.longitude,
      );
      _updateLocation(locationData);
      print('LocationService: Background location updated - ${locationData.city}');
    } catch (e) {
      print('LocationService: Failed to process background location - $e');
    }
  }

  /// Motion change handler
  void _onMotionChange(bg.Location location) async {
    print('LocationService: Motion changed - moving: ${location.isMoving}');
  }

  /// Activity change handler
  void _onActivityChange(bg.ActivityChangeEvent event) {
    print('LocationService: Activity changed - ${event.activity}');
  }

  /// Provider change handler
  void _onProviderChange(bg.ProviderChangeEvent event) {
    print('LocationService: Provider changed - GPS: ${event.gps}, Network: ${event.network}');
  }

  /// Start location tracking
  Future<void> startTracking() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      await bg.BackgroundGeolocation.start();
      print('LocationService: Started tracking');
    } catch (e) {
      print('LocationService: Failed to start tracking - $e');
    }
  }

  /// Stop location tracking
  Future<void> stopTracking() async {
    try {
      await bg.BackgroundGeolocation.stop();
      print('LocationService: Stopped tracking');
    } catch (e) {
      print('LocationService: Failed to stop tracking - $e');
    }
  }

  /// Calculate distance between two points in kilometers
  static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    return Geolocator.distanceBetween(lat1, lon1, lat2, lon2) / 1000;
  }

  /// Dispose resources
  void dispose() {
    _locationController.close();
    bg.BackgroundGeolocation.removeListeners();
  }
}

/// Location data model
class LocationData {
  final double latitude;
  final double longitude;
  final String? city;
  final DateTime timestamp;

  const LocationData({
    required this.latitude,
    required this.longitude,
    this.city,
    required this.timestamp,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationData &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.city == city;
  }

  @override
  int get hashCode {
    return latitude.hashCode ^ longitude.hashCode ^ city.hashCode;
  }

  @override
  String toString() {
    return 'LocationData(lat: $latitude, lng: $longitude, city: $city, time: $timestamp)';
  }
}
