import 'dart:async';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  final StreamController<LocationData> _locationController = StreamController<LocationData>.broadcast();
  Stream<LocationData> get locationStream => _locationController.stream;

  LocationData? _currentLocation;
  LocationData? get currentLocation => _currentLocation;

  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  bool _isAppInForeground = true;
  Timer? _periodicLocationTimer;

  /// Initialize location service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Request permissions first
      await _requestPermissions();

      // Get initial location first
      await _getCurrentLocation();

      // Start periodic location updates (every 1 hour)
      _startPeriodicLocationUpdates();

      _isInitialized = true;
      print('LocationService: Initialized successfully');
    } catch (e) {
      print('LocationService: Failed to initialize - $e');
      throw Exception('Failed to initialize location service: $e');
    }
  }

  /// Start periodic location updates (every 1 hour)
  void _startPeriodicLocationUpdates() {
    // Cancel any existing timer
    _periodicLocationTimer?.cancel();

    // Update location every 1 hour
    _periodicLocationTimer = Timer.periodic(const Duration(hours: 1), (timer) async {
      try {
        print('LocationService: Periodic location update triggered');
        await _getCurrentLocation();
      } catch (e) {
        print('LocationService: Failed to get periodic location update - $e');
      }
    });

    print('LocationService: Started periodic location updates (every 1 hour)');
  }

  /// Set app foreground/background state
  void setAppState({required bool isInForeground}) {
    _isAppInForeground = isInForeground;

    if (isInForeground) {
      print('LocationService: App in foreground - enabling real-time location');
      // When app comes to foreground, get current location immediately
      _getCurrentLocation();
    } else {
      print('LocationService: App in background - using periodic updates only');
    }
  }

  /// Request location permissions
  Future<void> _requestPermissions() async {
    LocationPermission permission = await Geolocator.checkPermission();

    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
    }

    if (permission == LocationPermission.deniedForever) {
      throw Exception('Location permissions are permanently denied');
    }

    if (permission == LocationPermission.denied) {
      throw Exception('Location permissions denied');
    }
  }

  /// Get current location immediately
  Future<LocationData?> _getCurrentLocation() async {
    try {
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      final locationData = await _createLocationData(position.latitude, position.longitude);
      _updateLocation(locationData);
      return locationData;
    } catch (e) {
      print('LocationService: Failed to get current location - $e');
      return null;
    }
  }

  /// Create LocationData from coordinates
  Future<LocationData> _createLocationData(double latitude, double longitude) async {
    String? city;
    try {
      final placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        city = placemark.locality ?? placemark.subAdministrativeArea ?? placemark.administrativeArea;
      }
    } catch (e) {
      print('LocationService: Failed to get city name - $e');
    }

    return LocationData(
      latitude: latitude,
      longitude: longitude,
      city: city,
      timestamp: DateTime.now(),
    );
  }

  /// Update location and notify listeners
  void _updateLocation(LocationData locationData) {
    _currentLocation = locationData;
    _locationController.add(locationData);
    _saveLocationToPrefs(locationData);
  }

  /// Save location to SharedPreferences
  Future<void> _saveLocationToPrefs(LocationData location) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('last_latitude', location.latitude);
      await prefs.setDouble('last_longitude', location.longitude);
      await prefs.setString('last_city', location.city ?? '');
      await prefs.setInt('last_location_timestamp', location.timestamp.millisecondsSinceEpoch);
    } catch (e) {
      print('LocationService: Failed to save location to prefs - $e');
    }
  }

  /// Load location from SharedPreferences
  Future<LocationData?> loadLocationFromPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final latitude = prefs.getDouble('last_latitude');
      final longitude = prefs.getDouble('last_longitude');
      final city = prefs.getString('last_city');
      final timestamp = prefs.getInt('last_location_timestamp');

      if (latitude != null && longitude != null && timestamp != null) {
        final locationData = LocationData(
          latitude: latitude,
          longitude: longitude,
          city: city?.isEmpty == true ? null : city,
          timestamp: DateTime.fromMillisecondsSinceEpoch(timestamp),
        );
        _currentLocation = locationData;
        return locationData;
      }
    } catch (e) {
      print('LocationService: Failed to load location from prefs - $e');
    }
    return null;
  }

  /// Get current location on demand (for foreground use)
  Future<LocationData?> getCurrentLocationOnDemand() async {
    if (_isAppInForeground) {
      return await _getCurrentLocation();
    } else {
      // In background, return cached location
      return _currentLocation;
    }
  }

  /// Calculate distance between two points in kilometers
  static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    return Geolocator.distanceBetween(lat1, lon1, lat2, lon2) / 1000;
  }

  /// Dispose resources
  void dispose() {
    _locationController.close();
    _periodicLocationTimer?.cancel();
  }
}

// Background location tracking removed - using periodic updates instead

/// Location data model
class LocationData {
  final double latitude;
  final double longitude;
  final String? city;
  final DateTime timestamp;

  const LocationData({
    required this.latitude,
    required this.longitude,
    this.city,
    required this.timestamp,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationData &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.city == city;
  }

  @override
  int get hashCode {
    return latitude.hashCode ^ longitude.hashCode ^ city.hashCode;
  }

  @override
  String toString() {
    return 'LocationData(lat: $latitude, lng: $longitude, city: $city, time: $timestamp)';
  }
}
