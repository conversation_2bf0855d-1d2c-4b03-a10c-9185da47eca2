import 'package:cloud_firestore/cloud_firestore.dart';
import 'otp_service.dart';

class AdminService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Verifies a user's document and sends notification email
  /// 
  /// [userId] - The user's UID
  /// [isApproved] - Whether the document is approved or rejected
  /// [rejectionReason] - Reason for rejection (if applicable)
  /// 
  /// This function should be called by admin when reviewing documents
  static Future<void> verifyUserDocument({
    required String userId,
    required bool isApproved,
    String? rejectionReason,
  }) async {
    try {
      // Get user data
      final userDoc = await _firestore.collection('users').doc(userId).get();
      
      if (!userDoc.exists) {
        throw Exception('User not found');
      }
      
      final userData = userDoc.data()!;
      final userEmail = userData['email'] as String?;
      final userName = userData['displayName'] as String?;
      final documentType = userData['documentType'] as String?;
      
      if (userEmail == null) {
        throw Exception('User email not found');
      }
      
      // Update document verification status
      await _firestore.collection('users').doc(userId).update({
        'documentVerified': isApproved ? 'true' : 'false',
        'documentVerifiedAt': FieldValue.serverTimestamp(),
        'documentRejectionReason': rejectionReason,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      if (isApproved) {
        // Send document verified notification
        try {
          await OTPService.sendDocumentVerifiedNotification(
            email: userEmail,
            userName: userName,
            documentType: documentType,
          );
          print('✅ Document verified notification sent to $userEmail');
        } catch (e) {
          print('⚠️ Failed to send document verified notification: $e');
          // Don't throw error as the main verification succeeded
        }
      } else {
        // TODO: In the future, you might want to send a rejection notification email
        print('📄 Document rejected for user $userEmail. Reason: $rejectionReason');
      }
      
    } catch (e) {
      throw Exception('Failed to verify document: $e');
    }
  }

  /// Gets all users with pending document verification
  /// 
  /// Returns a list of users who have uploaded documents but are pending verification
  static Future<List<Map<String, dynamic>>> getPendingDocumentVerifications() async {
    try {
      final querySnapshot = await _firestore
          .collection('users')
          .where('documentVerified', isEqualTo: 'pending')
          .orderBy('documentUploadedAt', descending: true)
          .get();
      
      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        data['uid'] = doc.id; // Add the document ID as uid
        return data;
      }).toList();
    } catch (e) {
      throw Exception('Failed to get pending verifications: $e');
    }
  }

  /// Gets user verification statistics
  /// 
  /// Returns statistics about user verifications
  static Future<Map<String, int>> getVerificationStats() async {
    try {
      final allUsers = await _firestore.collection('users').get();
      
      int totalUsers = allUsers.docs.length;
      int emailVerified = 0;
      int phoneVerified = 0;
      int documentVerified = 0;
      int documentPending = 0;
      int fullyVerified = 0;
      
      for (final doc in allUsers.docs) {
        final data = doc.data();
        
        final isEmailVerified = data['emailVerified'] == true;
        final isPhoneVerified = data['phoneVerified'] == true;
        final docStatus = data['documentVerified'];
        final isDocumentVerified = docStatus == 'true';
        final isDocumentPending = docStatus == 'pending';
        
        if (isEmailVerified) emailVerified++;
        if (isPhoneVerified) phoneVerified++;
        if (isDocumentVerified) documentVerified++;
        if (isDocumentPending) documentPending++;
        
        // Fully verified = email + phone + document all verified
        if (isEmailVerified && isPhoneVerified && isDocumentVerified) {
          fullyVerified++;
        }
      }
      
      return {
        'totalUsers': totalUsers,
        'emailVerified': emailVerified,
        'phoneVerified': phoneVerified,
        'documentVerified': documentVerified,
        'documentPending': documentPending,
        'fullyVerified': fullyVerified,
      };
    } catch (e) {
      throw Exception('Failed to get verification stats: $e');
    }
  }

  /// Example function to approve a document (for testing)
  /// 
  /// This is a helper function that can be used for testing the document verification flow
  static Future<void> approveDocumentForTesting(String userEmail) async {
    try {
      // Find user by email
      final querySnapshot = await _firestore
          .collection('users')
          .where('email', isEqualTo: userEmail)
          .limit(1)
          .get();
      
      if (querySnapshot.docs.isEmpty) {
        throw Exception('User with email $userEmail not found');
      }
      
      final userDoc = querySnapshot.docs.first;
      final userId = userDoc.id;
      
      // Verify the document
      await verifyUserDocument(
        userId: userId,
        isApproved: true,
      );
      
      print('✅ Document approved for user: $userEmail');
    } catch (e) {
      throw Exception('Failed to approve document: $e');
    }
  }

  /// Example function to reject a document (for testing)
  /// 
  /// This is a helper function that can be used for testing the document rejection flow
  static Future<void> rejectDocumentForTesting(String userEmail, String reason) async {
    try {
      // Find user by email
      final querySnapshot = await _firestore
          .collection('users')
          .where('email', isEqualTo: userEmail)
          .limit(1)
          .get();
      
      if (querySnapshot.docs.isEmpty) {
        throw Exception('User with email $userEmail not found');
      }
      
      final userDoc = querySnapshot.docs.first;
      final userId = userDoc.id;
      
      // Reject the document
      await verifyUserDocument(
        userId: userId,
        isApproved: false,
        rejectionReason: reason,
      );
      
      print('❌ Document rejected for user: $userEmail. Reason: $reason');
    } catch (e) {
      throw Exception('Failed to reject document: $e');
    }
  }
}
