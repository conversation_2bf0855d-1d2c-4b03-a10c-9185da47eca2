import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/location_service.dart';
import '../services/home_cache_service.dart';

/// Items state model
class ItemsState {
  final List<Map<String, dynamic>> items;
  final bool isLoading;
  final bool isRefreshing;
  final String? error;
  final LocationData? lastLocation;
  final DateTime? lastUpdate;
  final bool hasNewItems;
  final int newItemsCount;

  const ItemsState({
    this.items = const [],
    this.isLoading = false,
    this.isRefreshing = false,
    this.error,
    this.lastLocation,
    this.lastUpdate,
    this.hasNewItems = false,
    this.newItemsCount = 0,
  });

  ItemsState copyWith({
    List<Map<String, dynamic>>? items,
    bool? isLoading,
    bool? isRefreshing,
    String? error,
    LocationData? lastLocation,
    DateTime? lastUpdate,
    bool? hasNewItems,
    int? newItemsCount,
  }) {
    return ItemsState(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      error: error ?? this.error,
      lastLocation: lastLocation ?? this.lastLocation,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      hasNewItems: hasNewItems ?? this.hasNewItems,
      newItemsCount: newItemsCount ?? this.newItemsCount,
    );
  }
}

/// Items provider using Riverpod
class ItemsNotifier extends StateNotifier<ItemsState> {
  ItemsNotifier() : super(const ItemsState()) {
    _initialize();
  }

  final LocationService _locationService = LocationService();
  StreamSubscription<LocationData>? _locationSubscription;
  Timer? _backgroundRefreshTimer;

  /// Initialize the provider
  Future<void> _initialize() async {
    try {
      // Load cached location
      final cachedLocation = await _locationService.loadLocationFromPrefs();
      if (cachedLocation != null) {
        state = state.copyWith(lastLocation: cachedLocation);
        await _loadInitialItems(cachedLocation);
      }

      // Initialize location service
      await _locationService.initialize();
      await _locationService.startTracking();

      // Listen to location updates
      _locationSubscription = _locationService.locationStream.listen(_onLocationUpdate);

      // Start background refresh timer (every 2 minutes)
      _startBackgroundRefresh();

    } catch (e) {
      state = state.copyWith(error: 'Failed to initialize: $e');
    }
  }

  /// Load initial items from cache or fetch new ones
  Future<void> _loadInitialItems(LocationData location) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final items = await HomeCacheService.getItemsWithBackgroundRefresh(
        userLatitude: location.latitude,
        userLongitude: location.longitude,
        onFreshData: (freshItems) {
          // Update with fresh data when available
          _updateItemsWithLocation(freshItems, location);
        },
      );

      _updateItemsWithLocation(items, location);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load items: $e',
      );
    }
  }

  /// Handle location updates
  void _onLocationUpdate(LocationData newLocation) async {
    final currentLocation = state.lastLocation;
    
    // Check if location changed significantly (more than 100 meters)
    if (currentLocation != null) {
      final distance = LocationService.calculateDistance(
        currentLocation.latitude,
        currentLocation.longitude,
        newLocation.latitude,
        newLocation.longitude,
      );
      
      // Only update if moved more than 100 meters
      if (distance < 0.1) return;
    }

    print('ItemsProvider: Location updated, fetching new items...');
    await _refreshItemsForLocation(newLocation);
  }

  /// Refresh items for new location
  Future<void> _refreshItemsForLocation(LocationData location) async {
    state = state.copyWith(isRefreshing: true, lastLocation: location);

    try {
      final newItems = await HomeCacheService.forceRefresh(
        userLatitude: location.latitude,
        userLongitude: location.longitude,
      );

      await _mergeNewItems(newItems, location);
    } catch (e) {
      state = state.copyWith(
        isRefreshing: false,
        error: 'Failed to refresh items: $e',
      );
    }
  }

  /// Merge new items with existing ones
  Future<void> _mergeNewItems(List<Map<String, dynamic>> newItems, LocationData location) async {
    final currentItems = List<Map<String, dynamic>>.from(state.items);
    final currentItemIds = currentItems.map((item) => item['docId']).toSet();
    
    // Find truly new items
    final actuallyNewItems = newItems.where((item) => !currentItemIds.contains(item['docId'])).toList();
    
    if (actuallyNewItems.isNotEmpty) {
      // Add new items to the beginning of the list
      final updatedItems = [...actuallyNewItems, ...currentItems];
      
      // Sort by distance from current location
      _sortItemsByDistance(updatedItems, location);
      
      state = state.copyWith(
        items: updatedItems,
        isRefreshing: false,
        lastUpdate: DateTime.now(),
        hasNewItems: true,
        newItemsCount: actuallyNewItems.length,
        lastLocation: location,
      );
      
      print('ItemsProvider: Added ${actuallyNewItems.length} new items');
    } else {
      // No new items, just update location and refresh state
      _updateItemsWithLocation(newItems, location);
    }
  }

  /// Update items with location data
  void _updateItemsWithLocation(List<Map<String, dynamic>> items, LocationData location) {
    _sortItemsByDistance(items, location);
    
    state = state.copyWith(
      items: items,
      isLoading: false,
      isRefreshing: false,
      lastUpdate: DateTime.now(),
      lastLocation: location,
    );
  }

  /// Sort items by distance from current location
  void _sortItemsByDistance(List<Map<String, dynamic>> items, LocationData location) {
    items.sort((a, b) {
      final aLat = a['latitude'] as double? ?? 0.0;
      final aLng = a['longitude'] as double? ?? 0.0;
      final bLat = b['latitude'] as double? ?? 0.0;
      final bLng = b['longitude'] as double? ?? 0.0;

      final aDistance = LocationService.calculateDistance(
        location.latitude, location.longitude, aLat, aLng,
      );
      final bDistance = LocationService.calculateDistance(
        location.latitude, location.longitude, bLat, bLng,
      );

      // Update distance in item data
      a['distance'] = aDistance;
      b['distance'] = bDistance;

      return aDistance.compareTo(bDistance);
    });
  }

  /// Start background refresh timer
  void _startBackgroundRefresh() {
    _backgroundRefreshTimer?.cancel();
    _backgroundRefreshTimer = Timer.periodic(const Duration(minutes: 2), (timer) {
      _backgroundRefresh();
    });
  }

  /// Background refresh without user interaction
  Future<void> _backgroundRefresh() async {
    final location = state.lastLocation;
    if (location == null || state.isRefreshing) return;

    try {
      final newItems = await HomeCacheService.getItemsWithBackgroundRefresh(
        userLatitude: location.latitude,
        userLongitude: location.longitude,
        onFreshData: (freshItems) {
          _mergeNewItems(freshItems, location);
        },
      );

      // Only merge if we got fresh data
      if (newItems.isNotEmpty) {
        await _mergeNewItems(newItems, location);
      }
    } catch (e) {
      print('ItemsProvider: Background refresh failed - $e');
    }
  }

  /// Manual refresh triggered by user
  Future<void> refreshItems() async {
    final location = state.lastLocation;
    if (location == null) return;

    await _refreshItemsForLocation(location);
  }

  /// Clear new items notification
  void clearNewItemsNotification() {
    state = state.copyWith(hasNewItems: false, newItemsCount: 0);
  }

  /// Force refresh with current location
  Future<void> forceRefresh() async {
    final currentLocation = _locationService.currentLocation;
    if (currentLocation != null) {
      await _refreshItemsForLocation(currentLocation);
    } else {
      // Try to get current location
      await _locationService.initialize();
      final location = _locationService.currentLocation;
      if (location != null) {
        await _refreshItemsForLocation(location);
      }
    }
  }

  @override
  void dispose() {
    _locationSubscription?.cancel();
    _backgroundRefreshTimer?.cancel();
    super.dispose();
  }
}

/// Items provider instance
final itemsProvider = StateNotifierProvider<ItemsNotifier, ItemsState>((ref) {
  return ItemsNotifier();
});

/// Location service provider
final locationServiceProvider = Provider<LocationService>((ref) {
  return LocationService();
});

/// Current location provider
final currentLocationProvider = StreamProvider<LocationData?>((ref) {
  final locationService = ref.watch(locationServiceProvider);
  return locationService.locationStream;
});
